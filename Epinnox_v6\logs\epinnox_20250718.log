2025-07-18 21:33:01,540 - main - INFO - Epinnox v6 starting up...
2025-07-18 21:33:01,557 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 21:33:01,558 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 21:33:01,558 - main - INFO - Performance monitoring initialized
2025-07-18 21:33:01,568 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:33:01,569 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 21:33:01,570 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 21:33:05,986 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 21:40:28,631 - main - INFO - Epinnox v6 starting up...
2025-07-18 21:40:28,646 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 21:40:28,647 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 21:40:28,647 - main - INFO - Performance monitoring initialized
2025-07-18 21:40:28,656 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:40:28,656 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 21:40:28,657 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 21:40:33,560 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 21:40:37,078 - core.prompt_optimizer - INFO - Prompt optimization database initialized
2025-07-18 21:40:37,079 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 21:40:37,080 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 21:40:37,080 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 21:40:37,590 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 21:40:38,444 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 21:40:39,688 - websocket - INFO - Websocket connected
2025-07-18 21:40:42,578 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 21:40:42,978 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 21:40:42,978 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 21:40:42,978 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 21:40:42,978 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 21:40:42,984 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 21:40:45,040 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 21:40:45,041 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 21:40:45,042 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 21:40:45,048 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 21:40:45,048 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 21:40:45,048 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 21:40:45,049 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:40:45,049 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 21:40:45,052 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 21:40:45,069 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 21:40:45,069 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 21:40:45,069 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 21:40:45,074 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_214045_e75ca945
2025-07-18 21:40:45,074 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_214045_e75ca945
2025-07-18 21:40:45,210 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 21:40:45,212 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 21:40:45,213 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 21:40:45,215 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 21:40:45,217 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 21:40:45,218 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 21:40:45,218 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 21:40:45,218 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 21:40:45,219 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 21:40:45,219 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 21:40:46,964 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 0/60 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 0/48 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 0/32 for BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:40:46,964 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:41:17,836 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:41:17,836 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:41:17,837 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:42:23,176 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:42:23,176 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:42:23,177 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:43:25,712 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:43:25,713 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:44:27,864 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:44:27,865 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:45:00,696 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 1/60 for BTC/USDT:USDT
2025-07-18 21:45:00,696 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:45:00,696 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 1/48 for BTC/USDT:USDT
2025-07-18 21:45:00,697 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 1/32 for BTC/USDT:USDT
2025-07-18 21:45:00,697 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:45:00,697 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 0/100 for BTC/USDT:USDT
2025-07-18 21:51:38,939 - main - INFO - Epinnox v6 starting up...
2025-07-18 21:51:38,954 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 21:51:38,954 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 21:51:38,954 - main - INFO - Performance monitoring initialized
2025-07-18 21:51:38,962 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:51:38,963 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 21:51:38,963 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 21:51:44,076 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 21:51:48,700 - core.prompt_optimizer - INFO - Prompt optimization database initialized
2025-07-18 21:51:48,701 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 21:51:48,702 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 21:51:48,702 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 21:51:49,108 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 21:51:50,000 - data.live_data_manager - INFO - 📊 Fetching historical data for BTC/USDT:USDT to populate buffers...
2025-07-18 21:51:50,035 - data.live_data_manager - WARNING - ⚠️ Failed to load historical 1m data for BTC/USDT:USDT
2025-07-18 21:51:50,037 - data.live_data_manager - WARNING - ⚠️ Failed to load historical 5m data for BTC/USDT:USDT
2025-07-18 21:51:50,038 - data.live_data_manager - WARNING - ⚠️ Failed to load historical 15m data for BTC/USDT:USDT
2025-07-18 21:51:50,038 - data.live_data_manager - INFO - ✅ Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 21:51:51,245 - websocket - INFO - Websocket connected
2025-07-18 21:51:54,199 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 21:51:54,610 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 21:51:54,610 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 21:51:54,611 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 21:51:54,611 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 21:51:54,616 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 21:51:56,691 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 21:51:56,692 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 21:51:56,693 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 21:51:56,697 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 21:51:56,698 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 21:51:56,698 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 21:51:56,698 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 21:51:56,698 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 21:51:56,701 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 21:51:56,707 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 21:51:56,707 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 21:51:56,707 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 21:51:56,714 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_215156_c0d31470
2025-07-18 21:51:56,715 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_215156_c0d31470
2025-07-18 21:51:56,852 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 21:51:56,854 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 21:51:56,854 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 21:51:56,854 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 21:51:56,854 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 21:51:56,854 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 21:51:56,856 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 21:51:56,858 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 21:51:56,859 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 21:51:56,859 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 21:51:56,860 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 21:51:56,860 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 21:51:56,860 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 21:51:58,587 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 0/60 for BTC/USDT:USDT
2025-07-18 21:51:58,587 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:51:58,588 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 0/48 for BTC/USDT:USDT
2025-07-18 21:51:58,588 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 0/32 for BTC/USDT:USDT
2025-07-18 21:51:58,588 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:51:58,588 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 21:52:31,846 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 0/60 for BTC/USDT:USDT
2025-07-18 21:52:31,846 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:52:31,847 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 0/48 for BTC/USDT:USDT
2025-07-18 21:52:31,847 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 0/32 for BTC/USDT:USDT
2025-07-18 21:52:31,847 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:52:31,847 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 21:53:02,701 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 0/60 for BTC/USDT:USDT
2025-07-18 21:53:02,701 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 21:53:02,701 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 0/48 for BTC/USDT:USDT
2025-07-18 21:53:02,702 - core.regime_detector - WARNING - Insufficient 15m data for regime detection: 0/32 for BTC/USDT:USDT
2025-07-18 21:53:02,702 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 21:53:02,702 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:02:04,490 - main - INFO - Epinnox v6 starting up...
2025-07-18 22:02:04,521 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 22:02:04,521 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 22:02:04,522 - main - INFO - Performance monitoring initialized
2025-07-18 22:02:04,531 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 22:02:04,548 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 22:02:04,549 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 22:02:09,185 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 22:02:13,503 - core.prompt_optimizer - INFO - Prompt optimization database initialized
2025-07-18 22:02:13,506 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 22:02:13,521 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 22:02:13,522 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 22:02:14,394 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 22:02:15,278 - data.live_data_manager - INFO - 📊 Fetching historical data for BTC/USDT:USDT to populate buffers...
2025-07-18 22:02:15,302 - data.live_data_manager - INFO - ✅ Historical 1m data loaded for BTC/USDT:USDT
2025-07-18 22:02:15,306 - data.live_data_manager - INFO - ✅ Historical 5m data loaded for BTC/USDT:USDT
2025-07-18 22:02:15,310 - data.live_data_manager - INFO - ✅ Historical 15m data loaded for BTC/USDT:USDT
2025-07-18 22:02:15,310 - data.live_data_manager - INFO - ✅ Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 22:02:16,543 - websocket - INFO - Websocket connected
2025-07-18 22:02:18,781 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 22:02:19,215 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 22:02:19,215 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 22:02:19,216 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 22:02:19,216 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 22:02:19,228 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 22:02:21,278 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 22:02:21,278 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 22:02:21,278 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 22:02:21,310 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 22:02:21,311 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 22:02:21,311 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 22:02:21,312 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 22:02:21,312 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 22:02:21,315 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 22:02:21,350 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 22:02:21,351 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 22:02:21,351 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 22:02:21,356 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_220221_a71899dc
2025-07-18 22:02:21,356 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_220221_a71899dc
2025-07-18 22:02:21,568 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 22:02:21,591 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 22:02:21,591 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 22:02:21,591 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 22:02:21,591 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 22:02:21,592 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 22:02:21,593 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 22:02:21,612 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 22:02:21,613 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 22:02:21,613 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 22:02:21,614 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 22:02:21,614 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 22:02:21,614 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 22:02:23,270 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 24/60 for BTC/USDT:USDT
2025-07-18 22:02:23,270 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:02:23,270 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 24/48 for BTC/USDT:USDT
2025-07-18 22:02:23,271 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:02:23,271 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:02:54,649 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 24/60 for BTC/USDT:USDT
2025-07-18 22:02:54,649 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:02:54,649 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 24/48 for BTC/USDT:USDT
2025-07-18 22:02:54,649 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:02:54,649 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:03:26,196 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 24/60 for BTC/USDT:USDT
2025-07-18 22:03:26,196 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:03:26,196 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 24/48 for BTC/USDT:USDT
2025-07-18 22:03:26,196 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:03:26,196 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:03:58,039 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 24/60 for BTC/USDT:USDT
2025-07-18 22:03:58,040 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:03:58,040 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 24/48 for BTC/USDT:USDT
2025-07-18 22:03:58,040 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:03:58,040 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:04:31,520 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 24/60 for BTC/USDT:USDT
2025-07-18 22:04:31,521 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:04:31,521 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 24/48 for BTC/USDT:USDT
2025-07-18 22:04:31,521 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:04:31,521 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 2/100 for BTC/USDT:USDT
2025-07-18 22:05:03,587 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:05:03,587 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:05:03,587 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:05:03,588 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:05:03,588 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 3/100 for BTC/USDT:USDT
2025-07-18 22:05:33,646 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:05:33,646 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:05:33,646 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:05:33,646 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:05:33,646 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:06:04,758 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:06:04,758 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:06:04,759 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:06:04,759 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:06:04,759 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:06:34,864 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:06:34,865 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:06:34,865 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:06:34,865 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:06:34,866 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 3/100 for BTC/USDT:USDT
2025-07-18 22:07:06,565 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:07:06,566 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:07:06,566 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:07:06,566 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:07:06,566 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 3/100 for BTC/USDT:USDT
2025-07-18 22:07:36,628 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:07:36,628 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:07:36,628 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:07:36,629 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:07:36,629 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:08:06,661 - core.volatility_pause_system - WARNING - Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:08:06,661 - core.regime_detector - WARNING - Insufficient 1m data for regime detection: 0/60 for BTC/USDT:USDT
2025-07-18 22:08:06,661 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:08:06,662 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:08:06,662 - core.advanced_microstructure - WARNING - Insufficient trade history for microstructure analysis: 1/100 for BTC/USDT:USDT
2025-07-18 22:21:41,627 - main - INFO - Epinnox v6 starting up...
2025-07-18 22:21:41,642 - core.performance_monitor - INFO - Performance monitoring started
2025-07-18 22:21:41,643 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-18 22:21:41,643 - main - INFO - Performance monitoring initialized
2025-07-18 22:21:41,652 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 22:21:41,653 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-18 22:21:41,654 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-18 22:21:46,000 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-18 22:21:49,789 - core.prompt_optimizer - INFO - Prompt optimization database initialized
2025-07-18 22:21:49,791 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 22:21:49,792 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-18 22:21:49,792 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-18 22:21:50,318 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-18 22:21:51,143 - data.live_data_manager - INFO - 📊 Fetching historical data for BTC/USDT:USDT to populate buffers...
2025-07-18 22:21:51,155 - data.live_data_manager - INFO - ✅ Historical 1m data loaded for BTC/USDT:USDT
2025-07-18 22:21:51,162 - data.live_data_manager - INFO - ✅ Historical 5m data loaded for BTC/USDT:USDT
2025-07-18 22:21:51,166 - data.live_data_manager - INFO - ✅ Historical 15m data loaded for BTC/USDT:USDT
2025-07-18 22:21:51,166 - data.live_data_manager - INFO - ✅ Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-18 22:21:52,436 - websocket - INFO - Websocket connected
2025-07-18 22:21:55,459 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-18 22:21:55,869 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-18 22:21:55,870 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-18 22:21:55,870 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-18 22:21:55,870 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-18 22:21:55,875 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-18 22:21:57,935 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-18 22:21:57,936 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-18 22:21:57,936 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-18 22:21:57,939 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-18 22:21:57,939 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-18 22:21:57,939 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 22:21:57,940 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-18 22:21:57,940 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-18 22:21:57,943 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-18 22:21:57,958 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-18 22:21:57,959 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-18 22:21:57,959 - storage.session_manager - INFO - Session Manager initialized
2025-07-18 22:21:57,963 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250718_222157_ce2535b9
2025-07-18 22:21:57,963 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250718_222157_ce2535b9
2025-07-18 22:21:58,107 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-18 22:21:58,110 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-18 22:21:58,110 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-18 22:21:58,110 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-18 22:21:58,111 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-18 22:21:58,111 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-18 22:21:58,112 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-18 22:21:58,115 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-18 22:21:58,116 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-18 22:21:58,116 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-18 22:21:58,116 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-18 22:21:58,117 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-18 22:21:58,117 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-18 22:21:59,679 - core.volatility_pause_system - INFO - Volatility system data status for BTC/USDT:USDT:
2025-07-18 22:21:59,680 - core.volatility_pause_system - INFO -   5m candles: 24/60
2025-07-18 22:21:59,680 - core.volatility_pause_system - INFO -   1m candles: 120/60
2025-07-18 22:21:59,680 - core.volatility_pause_system - INFO -   Recent trades: 200/100
2025-07-18 22:21:59,680 - core.volatility_pause_system - WARNING - ❌ Insufficient 5m data for volatility analysis: 24/60 for BTC/USDT:USDT
2025-07-18 22:21:59,680 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 24/48 for BTC/USDT:USDT
2025-07-18 22:21:59,681 - core.regime_detector - INFO - Regime detector data status for BTC/USDT:USDT:
2025-07-18 22:21:59,681 - core.regime_detector - INFO -   ✅ 1m: 120/60 candles
2025-07-18 22:21:59,681 - core.regime_detector - INFO -   ❌ 5m: 24/48 candles
2025-07-18 22:21:59,681 - core.regime_detector - INFO -   ✅ 15m: 64/32 candles
2025-07-18 22:21:59,681 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:21:59,682 - core.advanced_microstructure - INFO - Microstructure analyzer data status for BTC/USDT:USDT:
2025-07-18 22:21:59,682 - core.advanced_microstructure - INFO -   Recent trades: 200/100
2025-07-18 22:21:59,682 - core.advanced_microstructure - INFO -   Orderbook history: 0/50
2025-07-18 22:21:59,682 - core.advanced_microstructure - INFO -   Orderbook data available: ✅
2025-07-18 22:21:59,682 - core.advanced_microstructure - WARNING - ❌ Insufficient orderbook history for microstructure analysis: 0/50 for BTC/USDT:USDT
2025-07-18 22:22:30,287 - core.volatility_pause_system - INFO - Volatility system data status for BTC/USDT:USDT:
2025-07-18 22:22:30,287 - core.volatility_pause_system - INFO -   5m candles: 25/60
2025-07-18 22:22:30,287 - core.volatility_pause_system - INFO -   1m candles: 120/60
2025-07-18 22:22:30,287 - core.volatility_pause_system - INFO -   Recent trades: 200/100
2025-07-18 22:22:30,287 - core.volatility_pause_system - WARNING - ❌ Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:22:30,288 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:22:30,288 - core.regime_detector - INFO - Regime detector data status for BTC/USDT:USDT:
2025-07-18 22:22:30,288 - core.regime_detector - INFO -   ✅ 1m: 120/60 candles
2025-07-18 22:22:30,288 - core.regime_detector - INFO -   ❌ 5m: 25/48 candles
2025-07-18 22:22:30,288 - core.regime_detector - INFO -   ✅ 15m: 64/32 candles
2025-07-18 22:22:30,288 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:22:30,288 - core.advanced_microstructure - INFO - Microstructure analyzer data status for BTC/USDT:USDT:
2025-07-18 22:22:30,289 - core.advanced_microstructure - INFO -   Recent trades: 200/100
2025-07-18 22:22:30,289 - core.advanced_microstructure - INFO -   Orderbook history: 0/50
2025-07-18 22:22:30,289 - core.advanced_microstructure - INFO -   Orderbook data available: ✅
2025-07-18 22:22:30,289 - core.advanced_microstructure - WARNING - ❌ Insufficient orderbook history for microstructure analysis: 0/50 for BTC/USDT:USDT
2025-07-18 22:23:00,354 - core.volatility_pause_system - INFO - Volatility system data status for BTC/USDT:USDT:
2025-07-18 22:23:00,354 - core.volatility_pause_system - INFO -   5m candles: 25/60
2025-07-18 22:23:00,355 - core.volatility_pause_system - INFO -   1m candles: 120/60
2025-07-18 22:23:00,355 - core.volatility_pause_system - INFO -   Recent trades: 200/100
2025-07-18 22:23:00,355 - core.volatility_pause_system - WARNING - ❌ Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:23:00,355 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:23:00,355 - core.regime_detector - INFO - Regime detector data status for BTC/USDT:USDT:
2025-07-18 22:23:00,355 - core.regime_detector - INFO -   ✅ 1m: 120/60 candles
2025-07-18 22:23:00,355 - core.regime_detector - INFO -   ❌ 5m: 25/48 candles
2025-07-18 22:23:00,355 - core.regime_detector - INFO -   ✅ 15m: 64/32 candles
2025-07-18 22:23:00,355 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:23:00,355 - core.advanced_microstructure - INFO - Microstructure analyzer data status for BTC/USDT:USDT:
2025-07-18 22:23:00,356 - core.advanced_microstructure - INFO -   Recent trades: 200/100
2025-07-18 22:23:00,356 - core.advanced_microstructure - INFO -   Orderbook history: 0/50
2025-07-18 22:23:00,356 - core.advanced_microstructure - INFO -   Orderbook data available: ✅
2025-07-18 22:23:00,356 - core.advanced_microstructure - WARNING - ❌ Insufficient orderbook history for microstructure analysis: 0/50 for BTC/USDT:USDT
2025-07-18 22:23:30,538 - core.volatility_pause_system - INFO - Volatility system data status for BTC/USDT:USDT:
2025-07-18 22:23:30,538 - core.volatility_pause_system - INFO -   5m candles: 25/60
2025-07-18 22:23:30,538 - core.volatility_pause_system - INFO -   1m candles: 120/60
2025-07-18 22:23:30,538 - core.volatility_pause_system - INFO -   Recent trades: 200/100
2025-07-18 22:23:30,538 - core.volatility_pause_system - WARNING - ❌ Insufficient 5m data for volatility analysis: 25/60 for BTC/USDT:USDT
2025-07-18 22:23:30,539 - core.regime_detector - WARNING - Insufficient 5m data for regime detection: 25/48 for BTC/USDT:USDT
2025-07-18 22:23:30,539 - core.regime_detector - INFO - Regime detector data status for BTC/USDT:USDT:
2025-07-18 22:23:30,539 - core.regime_detector - INFO -   ✅ 1m: 120/60 candles
2025-07-18 22:23:30,539 - core.regime_detector - INFO -   ❌ 5m: 25/48 candles
2025-07-18 22:23:30,539 - core.regime_detector - INFO -   ✅ 15m: 64/32 candles
2025-07-18 22:23:30,540 - core.regime_detector - WARNING - Insufficient data across timeframes for reliable regime detection: BTC/USDT:USDT
2025-07-18 22:23:30,540 - core.advanced_microstructure - INFO - Microstructure analyzer data status for BTC/USDT:USDT:
2025-07-18 22:23:30,540 - core.advanced_microstructure - INFO -   Recent trades: 200/100
2025-07-18 22:23:30,540 - core.advanced_microstructure - INFO -   Orderbook history: 0/50
2025-07-18 22:23:30,540 - core.advanced_microstructure - INFO -   Orderbook data available: ✅
2025-07-18 22:23:30,540 - core.advanced_microstructure - WARNING - ❌ Insufficient orderbook history for microstructure analysis: 0/50 for BTC/USDT:USDT
