#!/usr/bin/env python3
"""
🚨 CRITICAL 5M CANDLE DATA FIX VALIDATION
Test script to validate the 5m candle data insufficiency issues have been resolved
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_5m_candle_aggregation():
    """Test the 5m candle aggregation and data pipeline"""
    print("🚨 CRITICAL 5M CANDLE DATA FIX VALIDATION")
    print("=" * 60)
    
    try:
        # Import required modules
        from data.live_data_manager import LiveDataManager
        
        print("✅ LiveDataManager imported successfully")
        
        # Initialize system
        live_data_manager = LiveDataManager()
        print("✅ LiveDataManager initialized successfully")
        
        # Test symbol
        test_symbol = "BTC/USDT:USDT"
        
        # Test 1: Check initial data availability
        print(f"\n📊 Testing initial data availability for {test_symbol}...")
        
        # Subscribe to symbol
        live_data_manager.subscribe_symbol(test_symbol, ['1m', '5m', '15m'])
        print(f"✅ Subscribed to {test_symbol}")
        
        # Wait for subscription
        time.sleep(3)
        
        # Test 2: Fetch 1m data first
        print(f"\n🔍 Fetching 1m candle data...")
        candles_1m = live_data_manager.get_chart_data(test_symbol, '1m', limit=120)
        print(f"📊 1m candles fetched: {len(candles_1m) if candles_1m else 0}")
        
        if candles_1m and len(candles_1m) > 0:
            print(f"  Sample 1m candle: {candles_1m[-1]}")
            print(f"  1m data format: {type(candles_1m[0])}")
        
        # Test 3: Fetch 5m data (this should trigger aggregation)
        print(f"\n🔍 Fetching 5m candle data...")
        candles_5m_initial = live_data_manager.get_chart_data(test_symbol, '5m', limit=96)
        print(f"📊 5m candles fetched (initial): {len(candles_5m_initial) if candles_5m_initial else 0}")
        
        # Test 4: Force aggregation if insufficient
        if not candles_5m_initial or len(candles_5m_initial) < 48:
            print(f"\n🔄 Forcing 5m aggregation from 1m data...")
            
            if candles_1m and len(candles_1m) >= 60:
                # Force aggregation
                live_data_manager._aggregate_timeframe(test_symbol, '5m', candles_1m)
                
                # Re-fetch 5m data
                candles_5m_aggregated = live_data_manager.get_chart_data(test_symbol, '5m', limit=96)
                print(f"📊 5m candles after aggregation: {len(candles_5m_aggregated) if candles_5m_aggregated else 0}")
                
                if candles_5m_aggregated and len(candles_5m_aggregated) > 0:
                    print(f"  Sample 5m candle: {candles_5m_aggregated[-1]}")
                    print(f"  5m data format: {type(candles_5m_aggregated[0])}")
                    
                    # Validate aggregation quality
                    if len(candles_5m_aggregated) >= 48:
                        print("✅ 5m aggregation successful - sufficient data for advanced systems")
                    else:
                        print(f"⚠️ 5m aggregation insufficient: {len(candles_5m_aggregated)}/48")
            else:
                print(f"❌ Insufficient 1m data for aggregation: {len(candles_1m) if candles_1m else 0}/60")
        
        # Test 5: Test the enhanced fetch_enriched_market_data logic
        print(f"\n🧪 Testing enhanced market data fetching...")
        
        # Simulate the fetch_enriched_market_data logic
        market_data = {}
        
        # Get 1m data
        candles_1m_enhanced = live_data_manager.get_chart_data(test_symbol, '1m', limit=120)
        market_data['candles'] = candles_1m_enhanced
        market_data['candles_1m'] = candles_1m_enhanced
        
        # Get 5m data with enhanced logic
        candles_5m_enhanced = live_data_manager.get_chart_data(test_symbol, '5m', limit=96)
        
        # Apply the enhanced fallback logic
        if not candles_5m_enhanced or len(candles_5m_enhanced) < 48:
            print(f"⚠️ Applying enhanced 5m fallback logic...")
            
            # Force aggregation
            if candles_1m_enhanced and len(candles_1m_enhanced) >= 60:
                print(f"🔄 Forcing 5m aggregation from {len(candles_1m_enhanced)} 1m candles")
                live_data_manager._aggregate_timeframe(test_symbol, '5m', candles_1m_enhanced)
                
                # Re-fetch
                candles_5m_enhanced = live_data_manager.get_chart_data(test_symbol, '5m', limit=96)
                print(f"📊 Enhanced 5m fetch result: {len(candles_5m_enhanced) if candles_5m_enhanced else 0} candles")
        
        market_data['candles_5m'] = candles_5m_enhanced
        
        # Test 6: Validate data sufficiency for advanced systems
        print(f"\n✅ Testing data sufficiency for advanced systems...")
        
        candles_1m_count = len(candles_1m_enhanced) if candles_1m_enhanced else 0
        candles_5m_count = len(candles_5m_enhanced) if candles_5m_enhanced else 0
        
        requirements = {
            'volatility_system': {
                '1m_candles': {'actual': candles_1m_count, 'required': 60},
                '5m_candles': {'actual': candles_5m_count, 'required': 60}
            },
            'regime_detector': {
                '1m_candles': {'actual': candles_1m_count, 'required': 60},
                '5m_candles': {'actual': candles_5m_count, 'required': 48}
            }
        }
        
        print("📊 Data sufficiency analysis:")
        all_sufficient = True
        
        for system, reqs in requirements.items():
            print(f"  {system}:")
            system_sufficient = True
            for data_type, req in reqs.items():
                sufficient = req['actual'] >= req['required']
                status = "✅" if sufficient else "❌"
                print(f"    {status} {data_type}: {req['actual']}/{req['required']}")
                if not sufficient:
                    system_sufficient = False
                    all_sufficient = False
            
            sys_status = "✅" if system_sufficient else "❌"
            print(f"  {sys_status} {system}: {'READY' if system_sufficient else 'INSUFFICIENT DATA'}")
        
        # Test 7: Aggregation quality validation
        print(f"\n🔬 Testing aggregation quality...")
        
        if candles_1m_enhanced and candles_5m_enhanced:
            # Calculate expected 5m candles from 1m data
            expected_5m_candles = len(candles_1m_enhanced) // 5
            actual_5m_candles = len(candles_5m_enhanced)
            
            print(f"  1m candles available: {len(candles_1m_enhanced)}")
            print(f"  Expected 5m candles (simple): {expected_5m_candles}")
            print(f"  Actual 5m candles: {actual_5m_candles}")
            
            if actual_5m_candles >= expected_5m_candles * 0.8:  # Allow 20% tolerance
                print("✅ Aggregation quality: GOOD")
            else:
                print("⚠️ Aggregation quality: POOR - significant data loss")
        
        # Final summary
        print(f"\n{'='*60}")
        print(f"🚨 CRITICAL 5M CANDLE DATA FIX VALIDATION SUMMARY")
        print(f"{'='*60}")
        
        if all_sufficient:
            print("✅ ALL SYSTEMS HAVE SUFFICIENT 5M DATA")
            print("✅ 5m candle data pipeline issues RESOLVED")
            print("✅ Advanced systems can operate in FULL_ADVANCED mode")
        else:
            print("⚠️ Some systems still have insufficient 5m data")
            print("⚠️ Systems may operate in degraded mode")
            print("⚠️ Check 5m aggregation and fallback logic")
        
        print(f"\nTest completed at: {datetime.now()}")
        return all_sufficient
        
    except Exception as e:
        print(f"❌ Critical error in 5m candle test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_5m_candle_aggregation()
    sys.exit(0 if success else 1)
