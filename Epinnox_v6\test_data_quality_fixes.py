#!/usr/bin/env python3
"""
🚨 CRITICAL DATA QUALITY AND AGGREGATION FIX VALIDATION
Test script to validate all data quality and processing efficiency improvements
"""

import sys
import os
import time
import logging
import warnings
import numpy as np
from datetime import datetime

# Suppress numpy warnings to test if our fixes work
warnings.filterwarnings('ignore', category=RuntimeWarning)

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_data_quality_improvements():
    """Test all data quality and aggregation improvements"""
    print("🚨 CRITICAL DATA QUALITY AND AGGREGATION FIX VALIDATION")
    print("=" * 70)
    
    try:
        # Import required modules
        from data.live_data_manager import LiveDataManager
        from core.regime_detector import RegimeDetector
        from core.volatility_pause_system import VolatilityPauseSystem
        from core.advanced_microstructure import AdvancedMicrostructureAnalyzer
        from core.me2_stable import fetch_recent_trades, fetch_ohlcv
        
        print("✅ All modules imported successfully")
        
        # Test 1: Demo Data Quality
        print(f"\n📊 Testing demo data quality improvements...")
        
        test_symbol = "BTC/USDT:USDT"
        
        # Test OHLCV demo data generation
        demo_ohlcv = fetch_ohlcv(test_symbol, '1m', 120)
        has_nan = has_inf = invalid_high_low = negative_prices = False

        if demo_ohlcv is not None and not demo_ohlcv.empty:
            # Check for NaN/inf values
            has_nan = demo_ohlcv.isna().any().any()
            has_inf = np.isinf(demo_ohlcv.select_dtypes(include=[np.number])).any().any()

            print(f"  Demo OHLCV data: {len(demo_ohlcv)} candles")
            print(f"  Contains NaN values: {'❌ YES' if has_nan else '✅ NO'}")
            print(f"  Contains inf values: {'❌ YES' if has_inf else '✅ NO'}")

            # Check price relationships
            invalid_high_low = (demo_ohlcv['high'] < demo_ohlcv['low']).any()
            negative_prices = (demo_ohlcv[['open', 'high', 'low', 'close']] <= 0).any().any()

            print(f"  Invalid high < low: {'❌ YES' if invalid_high_low else '✅ NO'}")
            print(f"  Negative prices: {'❌ YES' if negative_prices else '✅ NO'}")
        else:
            print(f"  ❌ Demo OHLCV data generation failed")
        
        # Test 2: Missing Function Fix
        print(f"\n🔧 Testing missing fetch_recent_trades function...")
        
        try:
            demo_trades = fetch_recent_trades(test_symbol, 100)
            print(f"  ✅ fetch_recent_trades function working: {len(demo_trades)} trades generated")
            
            # Check trade data quality
            if demo_trades:
                valid_trades = 0
                for trade in demo_trades:
                    if (isinstance(trade.get('price'), (int, float)) and 
                        isinstance(trade.get('amount'), (int, float)) and
                        trade.get('price') > 0 and trade.get('amount') > 0):
                        valid_trades += 1
                
                print(f"  Valid trades: {valid_trades}/{len(demo_trades)}")
        except ImportError as e:
            print(f"  ❌ Import error still exists: {e}")
        except Exception as e:
            print(f"  ❌ Function error: {e}")
        
        # Test 3: System Integration
        print(f"\n🧪 Testing system integration with quality improvements...")
        
        # Initialize systems
        live_data_manager = LiveDataManager()
        regime_detector = RegimeDetector()
        volatility_system = VolatilityPauseSystem()
        microstructure_analyzer = AdvancedMicrostructureAnalyzer()
        
        print("✅ All systems initialized")
        
        # Subscribe and fetch data
        live_data_manager.subscribe_symbol(test_symbol, ['1m', '5m', '15m'])
        time.sleep(2)
        
        # Test data fetching
        candles_1m = live_data_manager.get_chart_data(test_symbol, '1m', limit=120)
        candles_5m = live_data_manager.get_chart_data(test_symbol, '5m', limit=96)
        candles_15m = live_data_manager.get_chart_data(test_symbol, '15m', limit=64)
        recent_trades = live_data_manager.get_recent_trades(test_symbol, limit=200)
        
        print(f"  Data fetched - 1m: {len(candles_1m) if candles_1m else 0}, "
              f"5m: {len(candles_5m) if candles_5m else 0}, "
              f"15m: {len(candles_15m) if candles_15m else 0}, "
              f"trades: {len(recent_trades) if recent_trades else 0}")
        
        # Test 4: Advanced System Analysis (should not produce warnings)
        print(f"\n🧠 Testing advanced systems without NumPy warnings...")
        
        market_data = {
            'candles': candles_1m,
            'candles_1m': candles_1m,
            'candles_5m': candles_5m,
            'candles_15m': candles_15m,
            'recent_trades': recent_trades,
            'orderbook': {'bids': [[50000, 1.0]], 'asks': [[50001, 1.0]]},
            'volume_24h': 1000000,
            'best_bid': 50000.0,
            'best_ask': 50001.0
        }
        
        # Capture warnings during analysis
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # Test regime detection
            regime_result = regime_detector.detect_regime(test_symbol, market_data)
            regime_warnings = len(w)
            
            # Test volatility analysis
            volatility_result = volatility_system.analyze_volatility(test_symbol, market_data)
            volatility_warnings = len(w) - regime_warnings
            
            # Test microstructure analysis
            microstructure_result = microstructure_analyzer.analyze_microstructure(test_symbol, market_data)
            microstructure_warnings = len(w) - regime_warnings - volatility_warnings
        
        print(f"  Regime detector: {'✅ SUCCESS' if regime_result else '⚠️ DEGRADED'} ({regime_warnings} warnings)")
        print(f"  Volatility system: {'✅ SUCCESS' if volatility_result else '⚠️ DEGRADED'} ({volatility_warnings} warnings)")
        print(f"  Microstructure analyzer: {'✅ SUCCESS' if microstructure_result else '⚠️ DEGRADED'} ({microstructure_warnings} warnings)")
        
        total_warnings = len(w)
        print(f"  Total NumPy warnings: {total_warnings}")
        
        # Test 5: Historical Data Accumulation
        print(f"\n📈 Testing historical data accumulation improvements...")
        
        # Check ATR history in volatility system
        atr_history_count = len(volatility_system.atr_history) if hasattr(volatility_system, 'atr_history') else 0
        print(f"  ATR history: {atr_history_count}/72 (baseline requirement)")
        
        # Check orderbook history in microstructure analyzer
        orderbook_history_count = len(microstructure_analyzer.orderbook_history) if hasattr(microstructure_analyzer, 'orderbook_history') else 0
        print(f"  Orderbook history: {orderbook_history_count}/50 (minimum requirement)")
        
        # Test 6: Aggregation Efficiency
        print(f"\n⚡ Testing aggregation efficiency improvements...")
        
        # Test multiple aggregation calls (should be efficient)
        start_time = time.time()
        for i in range(3):
            live_data_manager._aggregate_timeframe(test_symbol, '5m', candles_1m)
        aggregation_time = time.time() - start_time
        
        print(f"  Multiple aggregation calls: {aggregation_time:.3f}s (should be fast due to efficiency checks)")
        
        # Final Assessment
        print(f"\n{'='*70}")
        print(f"🚨 CRITICAL DATA QUALITY FIX VALIDATION SUMMARY")
        print(f"{'='*70}")
        
        issues_fixed = 0
        total_issues = 6
        
        # Issue 1: Demo data quality
        if not (has_nan or has_inf or invalid_high_low or negative_prices):
            print("✅ Demo data quality: FIXED (no NaN/inf/invalid values)")
            issues_fixed += 1
        else:
            print("❌ Demo data quality: ISSUES REMAIN")
        
        # Issue 2: Missing function
        if demo_trades and len(demo_trades) > 0:
            print("✅ Missing fetch_recent_trades function: FIXED")
            issues_fixed += 1
        else:
            print("❌ Missing fetch_recent_trades function: NOT FIXED")
        
        # Issue 3: NumPy warnings
        if total_warnings == 0:
            print("✅ NumPy warnings: ELIMINATED")
            issues_fixed += 1
        else:
            print(f"⚠️ NumPy warnings: {total_warnings} warnings still present")
        
        # Issue 4: ATR history
        if atr_history_count >= 72:
            print("✅ ATR historical data: SUFFICIENT")
            issues_fixed += 1
        else:
            print(f"⚠️ ATR historical data: {atr_history_count}/72 (building up)")
        
        # Issue 5: Orderbook history
        if orderbook_history_count >= 50:
            print("✅ Orderbook historical data: SUFFICIENT")
            issues_fixed += 1
        else:
            print(f"⚠️ Orderbook historical data: {orderbook_history_count}/50 (building up)")
        
        # Issue 6: Aggregation efficiency
        if aggregation_time < 1.0:  # Should be very fast
            print("✅ Aggregation efficiency: OPTIMIZED")
            issues_fixed += 1
        else:
            print(f"⚠️ Aggregation efficiency: {aggregation_time:.3f}s (may need optimization)")
        
        success_rate = (issues_fixed / total_issues) * 100
        print(f"\n🎯 Overall Success Rate: {success_rate:.1f}% ({issues_fixed}/{total_issues} issues resolved)")
        
        if success_rate >= 80:
            print("✅ DATA QUALITY AND AGGREGATION ISSUES SUBSTANTIALLY RESOLVED")
        else:
            print("⚠️ Some data quality issues remain - further optimization needed")
        
        print(f"\nTest completed at: {datetime.now()}")
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Critical error in data quality test: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_data_quality_improvements()
    sys.exit(0 if success else 1)
