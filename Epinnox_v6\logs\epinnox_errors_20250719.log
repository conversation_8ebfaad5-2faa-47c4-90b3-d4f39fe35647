2025-07-19 07:49:39,590 - core.advanced_microstructure - ERROR - <PERSON>rror calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:50:00,349 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:50:40,025 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:51:10,780 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:51:40,814 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:52:11,151 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:52:41,440 - core.advanced_microstructure - ERROR - <PERSON>rror calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:53:23,647 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 07:53:24,042 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:53:54,489 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:54:25,742 - core.advanced_microstructure - ERROR - Error calculating tick velocity: 'dict' object has no attribute 'timestamp'
2025-07-19 07:54:55,038 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x000001D483B3B020>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-19 07:54:55,038 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x000001D483B3B020>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-19 08:08:45,841 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:08:45,841 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:13:45,339 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:18:15,400 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:18:15,400 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:20:45,811 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:20:45,830 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:20:45,830 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:23:15,913 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:25:45,518 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:27:46,399 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:27:46,399 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:35:43,893 - websocket - ERROR - Connection to remote host was lost. - goodbye
2025-07-19 08:40:22,833 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:41:22,904 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:41:22,904 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:42:22,957 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:43:23,073 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:44:23,120 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:44:23,120 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:44:23,120 - llama.lmstudio_runner - ERROR - LMStudio request timed out
2025-07-19 08:48:23,235 - llama.lmstudio_runner - ERROR - LMStudio request timed out
